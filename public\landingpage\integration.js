/**
 * Integração entre Landing Page e Dashboard Administrativo
 * Este script captura leads e envia para o sistema Promandato
 */

// Função para detectar URL da API
function getApiUrl() {
    // Sempre usar localhost:3002 para desenvolvimento
    if (window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.port === '5500' ||
        window.location.port === '8080' ||
        window.location.port === '8000') {
        return 'http://localhost:3002';
    }

    // Se estiver rodando na mesma porta do backend
    if (window.location.port === '3002') {
        return window.location.origin;
    }

    // Produção (Firebase Hosting ou domínios customizados)
    if (window.location.hostname.includes('promandato.com') ||
        window.location.hostname.includes('firebaseapp.com') ||
        window.location.hostname.includes('web.app')) {
        return 'https://promandato-backend-517140455601.southamerica-east1.run.app';
    }

    // Fallback para desenvolvimento
    return 'http://localhost:3002';
}

// Configurações da API
const API_CONFIG = {
    baseURL: getApiUrl() + '/api',
    endpoints: {
        leads: '/leads',
        analytics: '/analytics/landing-page',
        demo: '/demo-requests',
        health: '/health'
    },
    // Flag para desabilitar analytics se endpoint não existir
    analyticsEnabled: true
};

// Classe para gerenciar leads da landing page
class LandingPageIntegration {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
        this.events = [];
        
        this.init();
    }

    async init() {
        // Inicializar tracking sem requisições imediatas
        // O analytics será enviado apenas quando o usuário interagir
        this.setupEventListeners();
        this.trackScrollDepth();
        this.trackTimeOnPage();

        // Atrasar o page view para evitar requisição imediata
        this.schedulePageViewTracking();

        console.log('Landing Page Integration inicializada (modo otimizado)');
    }

    // Agendar tracking de page view para evitar requisição imediata
    schedulePageViewTracking() {
        let pageViewSent = false;

        const sendPageView = () => {
            if (!pageViewSent) {
                pageViewSent = true;
                this.trackPageView();
            }
        };

        // Enviar page view apenas quando usuário interagir ou após 10 segundos
        const interactionEvents = ['scroll', 'click', 'mousemove', 'keydown', 'touchstart'];
        const listeners = [];

        interactionEvents.forEach(eventType => {
            const listener = () => {
                sendPageView();
                // Remover todos os listeners após o primeiro evento
                listeners.forEach(({ event, handler }) => {
                    document.removeEventListener(event, handler);
                });
            };
            document.addEventListener(eventType, listener, { once: true, passive: true });
            listeners.push({ event: eventType, handler: listener });
        });

        // Fallback: enviar após 10 segundos se não houver interação
        setTimeout(() => {
            sendPageView();
            listeners.forEach(({ event, handler }) => {
                document.removeEventListener(event, handler);
            });
        }, 10000);
    }

    // Verificar se o endpoint de analytics está disponível
    async checkAnalyticsAvailability() {
        try {
            const response = await fetch(`${API_CONFIG.baseURL}${API_CONFIG.endpoints.health}`);
            if (!response.ok) {
                throw new Error('Backend não disponível');
            }
            console.log('✅ Backend conectado - Analytics habilitado');
        } catch (error) {
            console.warn('⚠️ Backend não disponível - Analytics desabilitado:', error.message);
            API_CONFIG.analyticsEnabled = false;
        }
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Capturar parâmetros UTM
    getUTMParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            utm_source: urlParams.get('utm_source'),
            utm_medium: urlParams.get('utm_medium'),
            utm_campaign: urlParams.get('utm_campaign'),
            utm_term: urlParams.get('utm_term'),
            utm_content: urlParams.get('utm_content')
        };
    }

    // Obter informações do dispositivo
    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            screenResolution: `${screen.width}x${screen.height}`,
            viewportSize: `${window.innerWidth}x${window.innerHeight}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }

    // Tracking de visualização de página
    trackPageView() {
        const data = {
            event: 'page_view',
            sessionId: this.sessionId,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            referrer: document.referrer,
            utm: this.getUTMParameters(),
            device: this.getDeviceInfo()
        };

        this.sendAnalytics(data);
    }

    // Configurar event listeners
    setupEventListeners() {
        // Tracking de cliques em CTAs
        document.querySelectorAll('a[href="#demo"], a[href="#planos"], .cta-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.trackEvent('cta_click', {
                    element: e.target.textContent.trim(),
                    href: e.target.href,
                    section: this.getCurrentSection(e.target)
                });
            });
        });

        // Tracking de cliques em planos
        document.querySelectorAll('[id*="price"], .pricing-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const planType = this.extractPlanType(e.target);
                this.trackEvent('plan_click', {
                    plan: planType,
                    section: 'pricing'
                });
            });
        });

        // Tracking de formulários
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                this.handleFormSubmit(e);
            });
        });

        // Tracking de toggle de preços
        const billingToggle = document.getElementById('billing-toggle');
        if (billingToggle) {
            billingToggle.addEventListener('click', () => {
                this.trackEvent('billing_toggle', {
                    newState: billingToggle.classList.contains('bg-blue-600') ? 'yearly' : 'monthly'
                });
            });
        }
    }

    // Tracking de profundidade de scroll
    trackScrollDepth() {
        let maxScroll = 0;
        const milestones = [25, 50, 75, 90, 100];
        const tracked = new Set();

        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round(
                (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
            );
            
            maxScroll = Math.max(maxScroll, scrollPercent);

            milestones.forEach(milestone => {
                if (scrollPercent >= milestone && !tracked.has(milestone)) {
                    tracked.add(milestone);
                    this.trackEvent('scroll_depth', {
                        percentage: milestone
                    });
                }
            });
        });
    }

    // Tracking de tempo na página
    trackTimeOnPage() {
        // Enviar tempo na página quando usuário sair
        window.addEventListener('beforeunload', () => {
            const timeOnPage = Date.now() - this.startTime;
            this.trackEvent('time_on_page', {
                duration: timeOnPage,
                durationMinutes: Math.round(timeOnPage / 60000)
            });
        });

        // Tracking de tempo apenas em marcos importantes (sem polling constante)
        const milestones = [120000, 300000, 600000]; // 2min, 5min, 10min
        const trackedMilestones = new Set();

        // Verificar marcos apenas quando necessário (sem setInterval)
        const checkMilestones = () => {
            const timeOnPage = Date.now() - this.startTime;
            milestones.forEach(milestone => {
                if (timeOnPage >= milestone && !trackedMilestones.has(milestone)) {
                    trackedMilestones.add(milestone);
                    this.trackEvent('engagement_milestone', {
                        duration: timeOnPage,
                        durationSeconds: Math.round(timeOnPage / 1000),
                        milestone: `${Math.round(milestone / 60000)}min`
                    });
                }
            });
        };

        // Verificar marcos apenas em eventos de interação do usuário
        ['scroll', 'click', 'mousemove', 'keydown'].forEach(eventType => {
            let lastCheck = 0;
            document.addEventListener(eventType, () => {
                const now = Date.now();
                // Throttle: verificar no máximo a cada 30 segundos
                if (now - lastCheck > 30000) {
                    lastCheck = now;
                    checkMilestones();
                }
            }, { passive: true });
        });
    }

    // Extrair tipo de plano do elemento
    extractPlanType(element) {
        const text = element.textContent.toLowerCase();
        if (text.includes('básico') || text.includes('basic')) return 'basic';
        if (text.includes('padrão') || text.includes('standard')) return 'standard';
        if (text.includes('profissional') || text.includes('professional')) return 'professional';
        return 'unknown';
    }

    // Obter seção atual baseada no elemento
    getCurrentSection(element) {
        const section = element.closest('section');
        if (section) {
            return section.id || section.className.split(' ')[0] || 'unknown';
        }
        return 'unknown';
    }

    // Tracking de eventos genéricos
    trackEvent(eventName, data = {}) {
        const eventData = {
            event: eventName,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            ...data
        };

        this.events.push(eventData);
        this.sendAnalytics(eventData);
    }

    // Manipular envio de formulários
    async handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Adicionar dados de contexto
        const leadData = {
            ...data,
            sessionId: this.sessionId,
            timestamp: new Date().toISOString(),
            source: 'landing_page',
            utm: this.getUTMParameters(),
            device: this.getDeviceInfo(),
            referrer: document.referrer,
            timeOnPageBeforeSubmit: Date.now() - this.startTime
        };

        try {
            // Mostrar loading
            const submitButton = form.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Enviando...';
            submitButton.disabled = true;

            // Enviar lead
            const response = await this.submitLead(leadData);
            
            if (response.success) {
                // Tracking de conversão
                this.trackEvent('form_submit_success', {
                    formType: this.getFormType(form),
                    planInterest: data.planInterest || 'unknown'
                });

                // Mostrar mensagem de sucesso
                this.showSuccessMessage(form);
                
                // Limpar formulário
                form.reset();

                // Tracking para Google Analytics / Facebook Pixel
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'conversion', {
                        send_to: 'AW-CONVERSION_ID/CONVERSION_LABEL',
                        value: 1.0,
                        currency: 'BRL'
                    });
                }

                if (typeof fbq !== 'undefined') {
                    fbq('track', 'Lead', {
                        content_name: 'Landing Page Form',
                        value: 1.0,
                        currency: 'BRL'
                    });
                }

            } else {
                throw new Error(response.message || 'Erro ao enviar formulário');
            }

        } catch (error) {
            console.error('Erro ao enviar lead:', error);
            
            this.trackEvent('form_submit_error', {
                formType: this.getFormType(form),
                error: error.message
            });

            this.showErrorMessage(form, error.message);
        } finally {
            // Restaurar botão
            const submitButton = form.querySelector('button[type="submit"]');
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        }
    }

    // Identificar tipo de formulário
    getFormType(form) {
        if (form.id.includes('demo')) return 'demo_request';
        if (form.id.includes('contact')) return 'contact_form';
        return 'lead_form';
    }

    // Enviar lead para API
    async submitLead(leadData) {
        const response = await fetch(`${API_CONFIG.baseURL}${API_CONFIG.endpoints.leads}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(leadData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    // Enviar analytics
    async sendAnalytics(data) {
        // Se analytics estiver desabilitado, apenas logar localmente
        if (!API_CONFIG.analyticsEnabled) {
            console.log('📊 Analytics (local):', data.event, data);
            return;
        }

        try {
            const response = await fetch(`${API_CONFIG.baseURL}${API_CONFIG.endpoints.analytics}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                // Se o endpoint não existir, desabilitar analytics
                if (response.status === 404) {
                    console.warn('⚠️ Endpoint de analytics não encontrado - Desabilitando analytics');
                    API_CONFIG.analyticsEnabled = false;
                    return;
                }
                throw new Error(`HTTP ${response.status}`);
            }

            console.log('📊 Analytics enviado:', data.event);
        } catch (error) {
            console.warn('⚠️ Erro ao enviar analytics (continuando normalmente):', error.message);
            // Em caso de erro, desabilitar analytics para evitar spam de erros
            if (error.message.includes('Failed to fetch')) {
                API_CONFIG.analyticsEnabled = false;
                console.warn('🔇 Analytics desabilitado devido a problemas de conectividade');
            }
        }
    }

    // Mostrar mensagem de sucesso
    showSuccessMessage(form) {
        const successDiv = document.createElement('div');
        successDiv.className = 'bg-green-50 border border-green-200 rounded-lg p-4 mb-4';
        successDiv.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">
                        Formulário enviado com sucesso! Nossa equipe entrará em contato em breve.
                    </p>
                </div>
            </div>
        `;

        form.parentNode.insertBefore(successDiv, form);
        
        // Remover mensagem após 5 segundos
        setTimeout(() => {
            successDiv.remove();
        }, 5000);
    }

    // Mostrar mensagem de erro
    showErrorMessage(form, message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-4 mb-4';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">
                        ${message || 'Erro ao enviar formulário. Tente novamente.'}
                    </p>
                </div>
            </div>
        `;

        form.parentNode.insertBefore(errorDiv, form);
        
        // Remover mensagem após 5 segundos
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    // Obter estatísticas da sessão
    getSessionStats() {
        return {
            sessionId: this.sessionId,
            duration: Date.now() - this.startTime,
            eventsCount: this.events.length,
            events: this.events
        };
    }
}

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    window.LandingPageIntegration = new LandingPageIntegration();
});

// Exportar para uso global
window.LandingPageIntegration = LandingPageIntegration;
