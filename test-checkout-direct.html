<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Direto Checkout</title>
</head>
<body>
    <h1>Teste Direto do Endpoint de Checkout</h1>
    <button onclick="testCheckout()">Testar Checkout</button>
    <div id="result"></div>

    <script>
        async function testCheckout() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testando...';

            try {
                const requestBody = {
                    priceId: 'price_1RY5DHClUIoqY19kkU0D8pmf', // Basic monthly
                    planName: 'basic monthly',
                    billingCycle: 'monthly',
                    successUrl: 'https://landing-promandato.web.app/success.html?session_id={CHECKOUT_SESSION_ID}',
                    cancelUrl: 'https://landing-promandato.web.app/index.html',
                    customerEmail: '<EMAIL>'
                };

                console.log('📤 Enviando:', requestBody);

                const response = await fetch('https://promandato-backend-517140455601.southamerica-east1.run.app/api/stripe/create-checkout-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('📥 Status:', response.status, response.statusText);

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Sucesso:', data);
                    resultDiv.innerHTML = `
                        <h3>✅ Sucesso!</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    const errorText = await response.text();
                    console.error('❌ Erro:', errorText);
                    resultDiv.innerHTML = `
                        <h3>❌ Erro HTTP ${response.status}</h3>
                        <pre>${errorText}</pre>
                    `;
                }
            } catch (error) {
                console.error('❌ Erro de rede:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Erro de Rede</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
