/**
 * Configurações da Landing Page ProMandato
 * Configurações baseadas no ambiente
 */

// Detectar ambiente
const isLocalhost = (window.location.hostname === 'localhost' ||
                    window.location.hostname === '127.0.0.1') &&
                    (window.location.port === '5500' ||
                     window.location.port === '8080' ||
                     window.location.port === '8000');
const isStaging = window.location.hostname.includes('staging');
const isProduction = window.location.hostname.includes('firebaseapp.com') ||
                    window.location.hostname.includes('web.app') ||
                    window.location.hostname.includes('promandato.com') ||
                    window.location.hostname.includes('landing-promandato');

// Configurações por ambiente
const ENVIRONMENT_CONFIG = {
    development: {
        API_BASE_URL: 'http://localhost:3002',
        APP_URL: 'http://localhost:5174',
        STRIPE_PUBLIC_KEY: 'pk_test_...',
        FIREBASE_CONFIG: {
            // Configurações do Firebase para desenvolvimento
        }
    },
    staging: {
        API_BASE_URL: 'https://api-staging.promandato.com.br',
        APP_URL: 'https://promandato-staging.web.app',
        STRIPE_PUBLIC_KEY: 'pk_test_...',
        FIREBASE_CONFIG: {
            // Configurações do Firebase para staging
        }
    },
    production: {
        API_BASE_URL: 'https://promandato-backend-517140455601.southamerica-east1.run.app',
        APP_URL: 'https://www.promandato.com.br/app',
        STRIPE_PUBLIC_KEY: 'pk_live_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN',
        FIREBASE_CONFIG: {
            // Configurações do Firebase para produção
        }
    }
};

// Determinar ambiente atual - SEMPRE produção para landing page hospedada
let currentEnvironment = 'production';
if (isLocalhost) {
    currentEnvironment = 'development';
    console.log('🔧 Ambiente detectado: DESENVOLVIMENTO');
} else if (isStaging) {
    currentEnvironment = 'staging';
    console.log('🧪 Ambiente detectado: STAGING');
} else {
    console.log('🌐 Ambiente detectado: PRODUÇÃO');
}

// Exportar configurações
window.LANDING_CONFIG = {
    ENVIRONMENT: currentEnvironment,
    ...ENVIRONMENT_CONFIG[currentEnvironment],
    
    // Configurações gerais
    COMPANY: {
        name: 'ProMandato',
        email: '<EMAIL>',
        phone: '(11) 9999-9999',
        address: 'São Paulo, SP - Brasil'
    },
    
    // Configurações de analytics
    ANALYTICS: {
        GOOGLE_ANALYTICS_ID: 'GA_MEASUREMENT_ID',
        FACEBOOK_PIXEL_ID: 'FB_PIXEL_ID',
        HOTJAR_ID: 'HOTJAR_ID'
    },
    
    // Configurações de SEO
    SEO: {
        title: 'ProMandato - Gestão Política Inteligente',
        description: 'Plataforma completa para gestão de mandatos políticos com IA integrada. Gerencie demandas, cidadãos, agenda e muito mais.',
        keywords: 'gestão política, mandato, IA, inteligência artificial, política, governo, cidadãos',
        ogImage: 'https://promandato.com.br/og-image.jpg'
    },
    
    // Configurações de features
    FEATURES: {
        STRIPE_ENABLED: true,
        CONTACT_FORM_ENABLED: true,
        DEMO_REQUEST_ENABLED: true,
        ANALYTICS_ENABLED: currentEnvironment === 'production'
    }
};

console.log(`Landing Page carregada em ambiente: ${currentEnvironment}`);
