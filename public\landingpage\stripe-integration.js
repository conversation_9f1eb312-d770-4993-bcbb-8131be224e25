/**
 * Integração Stripe para Landing Page Promandato
 * Gerencia pagamentos e assinaturas diretamente da landing page
 */

// Função removida - detecção de ambiente agora está em getStripeConfig()

// Função para obter configuração do Stripe
function getStripeConfig() {
    // SEMPRE usar backend de produção para landing page hospedada
    let apiUrl;

    if (window.location.hostname.includes('landing-promandato') ||
        window.location.hostname.includes('firebaseapp.com') ||
        window.location.hostname.includes('web.app') ||
        window.location.hostname.includes('promandato.com')) {
        // Produção - sempre usar backend de produção
        apiUrl = 'https://promandato-backend-517140455601.southamerica-east1.run.app';
        console.log('🌐 Ambiente: PRODUÇÃO - usando backend de produção');
    } else {
        // Desenvolvimento local
        apiUrl = window.LANDING_CONFIG?.API_BASE_URL || 'http://localhost:3002';
        console.log('🔧 Ambiente: DESENVOLVIMENTO - usando backend local');
    }

    return {
        // Chave pública do Stripe
        publishableKey: 'pk_live_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN',

        // URLs de redirecionamento (fluxo correto do Stripe)
        successUrl: 'https://landing-promandato.web.app/success.html?session_id={CHECKOUT_SESSION_ID}',
        cancelUrl: 'https://landing-promandato.web.app/index.html',

        // URL da API - sempre produção para landing page hospedada
        apiUrl: apiUrl,

        // Configuração será carregada dinamicamente do backend
        prices: null,
        priceIds: null
    };
}

// Configuração do Stripe (será inicializada dinamicamente)
let STRIPE_CONFIG;

class StripeIntegration {
    constructor() {
        this.stripe = null;
        this.isYearlyBilling = false;
        this.config = null;
        this.init();
    }

    async init() {
        try {
            // Aguardar um pouco para garantir que config.js foi carregado
            await new Promise(resolve => setTimeout(resolve, 100));

            // Inicializar configuração
            this.config = getStripeConfig();
            STRIPE_CONFIG = this.config; // Para compatibilidade

            // Carregar dados do backend
            await this.loadBackendData();

            // Debug: mostrar configuração
            console.log('🔧 Stripe Config:', {
                apiUrl: this.config.apiUrl,
                currentOrigin: window.location.origin,
                currentHost: window.location.hostname,
                currentPort: window.location.port,
                configSource: window.LANDING_CONFIG ? 'config.js' : 'auto-detect',
                pricesLoaded: !!this.config.prices
            });

            // Verificar se está usando o backend correto
            if (this.config.apiUrl.includes('localhost')) {
                console.warn('⚠️ ATENÇÃO: Usando backend local em produção!');
            } else {
                console.log('✅ Usando backend de produção corretamente');
            }

            // Inicializar Stripe
            this.stripe = Stripe(this.config.publishableKey);

            // Configurar event listeners (sem health check automático)
            this.setupEventListeners();

            console.log('✅ Stripe integration initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing Stripe:', error);
            this.showError('Erro ao inicializar sistema de pagamentos. Tente recarregar a página.');
        }
    }

    /**
     * Carregar dados do backend (preços e price IDs)
     */
    async loadBackendData() {
        try {
            console.log('📊 Carregando dados do backend...');

            // Buscar planos públicos
            const plansResponse = await fetch(`${this.config.apiUrl}/api/public/plans`);
            if (!plansResponse.ok) {
                throw new Error(`Erro ao carregar planos: ${plansResponse.status}`);
            }

            const plansData = await plansResponse.json();
            console.log('Planos carregados:', plansData);

            // Processar dados dos planos
            this.config.prices = {};
            this.config.priceIds = {};

            if (plansData.success && plansData.data) {
                plansData.data.forEach(plan => {
                    const planKey = plan.id.toLowerCase();
                    this.config.prices[planKey] = plan.price;

                    console.log(`📋 Processando plano ${planKey}:`, plan.price);
                });

                console.log('✅ Preços processados:', this.config.prices);
            }

            // Configurar Price IDs do Stripe (mapeamento correto)
            this.config.priceIds = {
                basic: {
                    monthly: 'price_1RY5DHClUIoqY19kkU0D8pmf',
                    yearly: null
                },
                standard: {
                    monthly: 'price_1RY5FSClUIoqY19kh9kieCaY',
                    yearly: null
                },
                professional: {
                    monthly: 'price_1RY5GJClUIoqY19k1bCNt4lG',
                    yearly: null
                }
            };

            console.log('✅ Price IDs configurados:', this.config.priceIds);

            console.log('✅ Dados do backend carregados com sucesso');

        } catch (error) {
            console.error('❌ Erro ao carregar dados do backend:', error);

            // Usar dados fallback (valores reais da API)
            this.config.prices = {
                basic: { monthly: 169.9, yearly: 1834.92 },
                standard: { monthly: 259.9, yearly: 2807.08 },
                professional: { monthly: 599.9, yearly: 6478.92 }
            };

            this.config.priceIds = {
                basic: { monthly: 'price_1RY5DHClUIoqY19kkU0D8pmf', yearly: null },
                standard: { monthly: 'price_1RY5FSClUIoqY19kh9kieCaY', yearly: null },
                professional: { monthly: 'price_1RY5GJClUIoqY19k1bCNt4lG', yearly: null }
            };

            console.log('⚠️ Usando dados fallback');
        }
    }

    // Método removido - não fazer teste prévio de conexão

    /**
     * Obter Price ID do Stripe baseado no plano e ciclo de billing
     */
    getPriceId(planType, billingCycle) {
        const priceIds = this.config?.priceIds;
        if (!priceIds || !priceIds[planType]) {
            console.error(`Price ID não encontrado para plano: ${planType}`);
            return null;
        }

        const priceId = priceIds[planType][billingCycle];
        if (!priceId) {
            console.error(`Price ID não encontrado para ${planType} ${billingCycle}`);
            // Fallback para mensal se anual não estiver disponível
            if (billingCycle === 'yearly') {
                console.log('Usando preço mensal como fallback');
                return priceIds[planType]['monthly'];
            }
            return null;
        }

        return priceId;
    }

    setupEventListeners() {
        // Botões de planos
        document.querySelectorAll('.plan-button[data-plan]').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const plan = e.target.getAttribute('data-plan');
                this.handlePlanSelection(plan);
            });
        });

        // Toggle de billing (mensal/anual)
        document.querySelectorAll('.billing-toggle').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const billing = e.target.getAttribute('data-billing');
                this.isYearlyBilling = billing === 'yearly';
                this.updateBillingToggle();
                this.updatePriceDisplay();
            });
        });

        // Inicializar preços
        this.updatePriceDisplay();
    }

    updateBillingToggle() {
        const monthlyBtn = document.getElementById('monthly-btn');
        const yearlyBtn = document.getElementById('yearly-btn');

        if (this.isYearlyBilling) {
            monthlyBtn.classList.remove('bg-primary-600', 'text-white');
            monthlyBtn.classList.add('text-gray-600', 'hover:text-gray-900');
            yearlyBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            yearlyBtn.classList.add('bg-primary-600', 'text-white');
        } else {
            yearlyBtn.classList.remove('bg-primary-600', 'text-white');
            yearlyBtn.classList.add('text-gray-600', 'hover:text-gray-900');
            monthlyBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            monthlyBtn.classList.add('bg-primary-600', 'text-white');
        }
    }

    async handlePlanSelection(planType) {
        try {
            // Mostrar loading
            this.showLoading(true);

            // Obter billing cycle
            const billingCycle = this.isYearlyBilling ? 'yearly' : 'monthly';

            // Obter Price ID correto
            const priceId = this.getPriceId(planType, billingCycle);
            console.log('🔍 Price ID obtido:', { planType, billingCycle, priceId });

            if (!priceId) {
                console.error('❌ Price ID não encontrado para:', { planType, billingCycle });
                console.error('📋 Price IDs disponíveis:', this.config.priceIds);
                throw new Error(`Plano ${planType} ${billingCycle} não está disponível no momento.`);
            }

            // Capturar dados do lead se disponível
            const leadData = this.getLeadData();

            console.log('Criando checkout session:', {
                planType,
                billingCycle,
                priceId,
                apiUrl: this.config.apiUrl
            });

            // Tentar criar checkout session diretamente (sem teste prévio)
            return await this.createCheckoutSession(planType, billingCycle, priceId, leadData);

        } catch (error) {
            console.error('Error handling plan selection:', error);
            this.showError(`Erro ao processar pagamento. Tente novamente ou entre em contato conosco.`);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Criar checkout session (método separado para melhor organização)
     */
    async createCheckoutSession(planType, billingCycle, priceId, leadData) {
        try {
            // Criar checkout session via backend (formato exato que o backend espera)
            const requestBody = {
                priceId: priceId,
                planName: `${planType} ${billingCycle}`,
                billingCycle: billingCycle,
                successUrl: this.config.successUrl,
                cancelUrl: this.config.cancelUrl
            };

            // Adicionar email apenas se disponível
            if (leadData.email) {
                requestBody.customerEmail = leadData.email;
            }

            console.log('📤 Enviando requisição para backend:', requestBody);

            const response = await fetch(`${this.config.apiUrl}/api/stripe/create-checkout-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

        console.log('📥 Resposta do backend:', response.status, response.statusText);

        if (!response.ok) {
            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
            let errorDetails = null;

            try {
                const errorText = await response.text();
                console.error('❌ Resposta de erro (texto):', errorText);

                // Tentar fazer parse como JSON
                try {
                    errorDetails = JSON.parse(errorText);
                    errorMessage = errorDetails.error || errorDetails.message || errorMessage;
                    console.error('❌ Erro do backend (JSON):', errorDetails);
                } catch (parseError) {
                    console.error('❌ Erro não é JSON válido:', parseError);
                    errorMessage = errorText || errorMessage;
                }
            } catch (textError) {
                console.warn('Não foi possível ler resposta de erro:', textError);
            }

            throw new Error(errorMessage);
        }

        const sessionData = await response.json();
        console.log('✅ Checkout session criada:', sessionData);

        // Verificar se recebemos a resposta no formato correto
        if (!sessionData.success || !sessionData.data || !sessionData.data.sessionId) {
            throw new Error(sessionData.error || 'Erro ao criar sessão de checkout');
        }

        // Redirecionar para Stripe Checkout usando o sessionId correto
        const { error } = await this.stripe.redirectToCheckout({
            sessionId: sessionData.data.sessionId
        });

        if (error) {
            throw new Error(error.message || 'Erro no redirecionamento para checkout');
        }

        } catch (error) {
            console.error('Erro ao criar checkout session:', error);
            throw new Error(`Erro ao processar pagamento: ${error.message}`);
        }
    }

    // Método de fallback removido - foco na integração direta com Stripe

    getLeadData() {
        // Tentar capturar dados de formulários preenchidos
        const leadData = {
            name: '',
            email: '',
            phone: '',
            organization: '',
            source: 'landing_page_checkout',
            utm: this.getUTMParameters()
        };

        // Procurar por campos preenchidos na página
        const nameField = document.querySelector('input[name="name"]');
        const emailField = document.querySelector('input[name="email"]');
        const phoneField = document.querySelector('input[name="phone"]');
        const orgField = document.querySelector('input[name="organization"]');

        if (nameField) leadData.name = nameField.value;
        if (emailField) leadData.email = emailField.value;
        if (phoneField) leadData.phone = phoneField.value;
        if (orgField) leadData.organization = orgField.value;

        return leadData;
    }

    getUTMParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            utm_source: urlParams.get('utm_source'),
            utm_medium: urlParams.get('utm_medium'),
            utm_campaign: urlParams.get('utm_campaign'),
            utm_term: urlParams.get('utm_term'),
            utm_content: urlParams.get('utm_content')
        };
    }

    updatePriceDisplay() {
        if (!this.config || !this.config.prices) {
            console.warn('Preços não carregados ainda');
            return;
        }

        console.log('📊 Atualizando exibição de preços:', this.config.prices);

        // Atualizar exibição de preços baseado no billing cycle
        const prices = this.isYearlyBilling ? {
            basic: this.config.prices.basic.yearly / 12,
            standard: this.config.prices.standard.yearly / 12,
            professional: this.config.prices.professional.yearly / 12
        } : {
            basic: this.config.prices.basic.monthly,
            standard: this.config.prices.standard.monthly,
            professional: this.config.prices.professional.monthly
        };

        // Atualizar elementos de preço na página
        const basicPrice = document.getElementById('basic-price');
        const standardPrice = document.getElementById('standard-price');
        const professionalPrice = document.getElementById('professional-price');

        if (basicPrice) {
            basicPrice.textContent = this.formatPrice(prices.basic);
            console.log('Preço básico atualizado:', prices.basic);
        }
        if (standardPrice) {
            standardPrice.textContent = this.formatPrice(prices.standard);
            console.log('Preço padrão atualizado:', prices.standard);
        }
        if (professionalPrice) {
            professionalPrice.textContent = this.formatPrice(prices.professional);
            console.log('Preço profissional atualizado:', prices.professional);
        }

        // Mostrar/esconder indicadores de economia anual
        const basicSavings = document.getElementById('basic-yearly-savings');
        const standardSavings = document.getElementById('standard-yearly-savings');
        const professionalSavings = document.getElementById('professional-yearly-savings');

        if (this.isYearlyBilling && this.config.prices.basic.yearly) {
            const basicAnnualSavings = (this.config.prices.basic.monthly * 12) - this.config.prices.basic.yearly;
            const standardAnnualSavings = (this.config.prices.standard.monthly * 12) - this.config.prices.standard.yearly;
            const professionalAnnualSavings = (this.config.prices.professional.monthly * 12) - this.config.prices.professional.yearly;

            if (basicSavings && basicAnnualSavings > 0) {
                basicSavings.textContent = `Economize ${this.formatPrice(basicAnnualSavings)}/ano`;
                basicSavings.style.display = 'block';
            }
            if (standardSavings && standardAnnualSavings > 0) {
                standardSavings.textContent = `Economize ${this.formatPrice(standardAnnualSavings)}/ano`;
                standardSavings.style.display = 'block';
            }
            if (professionalSavings && professionalAnnualSavings > 0) {
                professionalSavings.textContent = `Economize ${this.formatPrice(professionalAnnualSavings)}/ano`;
                professionalSavings.style.display = 'block';
            }
        } else {
            if (basicSavings) basicSavings.style.display = 'none';
            if (standardSavings) standardSavings.style.display = 'none';
            if (professionalSavings) professionalSavings.style.display = 'none';
        }
    }

    /**
     * Formatar preço em Real brasileiro
     */
    formatPrice(price) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(price);
    }

    showLoading(show) {
        // Mostrar/esconder indicador de loading
        const loadingElements = document.querySelectorAll('.stripe-loading');
        const buttonElements = document.querySelectorAll('.plan-button[data-plan]');

        if (show) {
            // Mostrar loading
            loadingElements.forEach(el => el.classList.remove('hidden'));
            buttonElements.forEach(el => {
                el.disabled = true;
                // Armazenar texto original
                if (!el.dataset.originalText) {
                    el.dataset.originalText = el.textContent;
                }
                el.innerHTML = `
                    <div class="flex items-center justify-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processando...
                    </div>
                `;
            });
        } else {
            // Esconder loading
            loadingElements.forEach(el => el.classList.add('hidden'));
            buttonElements.forEach(el => {
                el.disabled = false;
                // Restaurar texto original
                if (el.dataset.originalText) {
                    el.textContent = el.dataset.originalText;
                }
            });
        }
    }

    showError(message) {
        // Mostrar mensagem de erro
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // Remover após 5 segundos
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    // Método para criar customer portal (gerenciar assinatura)
    async createCustomerPortal(customerId) {
        try {
            const response = await fetch('/api/stripe/create-customer-portal', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    customerId: customerId,
                    returnUrl: window.location.origin
                })
            });

            if (!response.ok) {
                throw new Error('Failed to create customer portal');
            }

            const { url } = await response.json();
            window.location.href = url;

        } catch (error) {
            console.error('Error creating customer portal:', error);
            this.showError('Erro ao acessar portal do cliente.');
        }
    }

    // Método para verificar status de pagamento
    async checkPaymentStatus(sessionId) {
        try {
            const response = await fetch(`/api/stripe/check-payment-status/${sessionId}`);
            
            if (!response.ok) {
                throw new Error('Failed to check payment status');
            }

            const status = await response.json();
            return status;

        } catch (error) {
            console.error('Error checking payment status:', error);
            return null;
        }
    }
}

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    window.StripeIntegration = new StripeIntegration();
});

// Exportar para uso global
window.StripeIntegration = StripeIntegration;
