/**
 * Integração Stripe para Landing Page Promandato
 * Gerencia pagamentos e assinaturas diretamente da landing page
 */

// Função para detectar a URL da API baseada no ambiente
function getApiUrl() {
    // Se estiver rodando no Live Server (porta 5500) ou similar
    if (window.location.port === '5500' || window.location.hostname === '127.0.0.1') {
        return 'http://localhost:3002';
    }

    // Se estiver rodando na mesma origem do backend
    if (window.location.port === '3002') {
        return window.location.origin;
    }

    // Fallback para desenvolvimento
    return 'http://localhost:3002';
}

// Configuração do Stripe
const STRIPE_CONFIG = {
    // Chave pública do Stripe
    publishableKey: 'pk_live_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN',
    
    // URLs de redirecionamento
    successUrl: window.location.origin + '/landingpage/success.html',
    cancelUrl: window.location.origin + '/landingpage/index.html',

    // URL da API - detectar automaticamente
    apiUrl: getApiUrl()
};

class StripeIntegration {
    constructor() {
        this.stripe = null;
        this.isYearlyBilling = false;
        this.init();
    }

    async init() {
        try {
            // Debug: mostrar configuração
            console.log('🔧 Stripe Config:', {
                apiUrl: STRIPE_CONFIG.apiUrl,
                currentOrigin: window.location.origin,
                currentHost: window.location.hostname,
                currentPort: window.location.port
            });

            // Inicializar Stripe
            this.stripe = Stripe(STRIPE_CONFIG.publishableKey);

            // Configurar event listeners (sem health check automático)
            this.setupEventListeners();

            console.log('✅ Stripe integration initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing Stripe:', error);
            this.showError('Erro ao inicializar sistema de pagamentos. Tente recarregar a página.');
        }
    }

    async testBackendConnection() {
        try {
            const response = await fetch(`${STRIPE_CONFIG.apiUrl}/api/health`);
            if (!response.ok) {
                throw new Error(`Backend não disponível: ${response.status}`);
            }
            console.log('Backend connection successful');
            return true;
        } catch (error) {
            console.warn('Backend connection failed:', error);
            this.showError('Servidor temporariamente indisponível. Tente novamente em alguns minutos.');
            return false;
        }
    }

    setupEventListeners() {
        // Botões de planos
        document.querySelectorAll('.plan-button[data-plan]').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const plan = e.target.getAttribute('data-plan');
                this.handlePlanSelection(plan);
            });
        });

        // Toggle de billing (mensal/anual)
        document.querySelectorAll('.billing-toggle').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const billing = e.target.getAttribute('data-billing');
                this.isYearlyBilling = billing === 'yearly';
                this.updateBillingToggle();
                this.updatePriceDisplay();
            });
        });

        // Inicializar preços
        this.updatePriceDisplay();
    }

    updateBillingToggle() {
        const monthlyBtn = document.getElementById('monthly-btn');
        const yearlyBtn = document.getElementById('yearly-btn');

        if (this.isYearlyBilling) {
            monthlyBtn.classList.remove('bg-primary-600', 'text-white');
            monthlyBtn.classList.add('text-gray-600', 'hover:text-gray-900');
            yearlyBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            yearlyBtn.classList.add('bg-primary-600', 'text-white');
        } else {
            yearlyBtn.classList.remove('bg-primary-600', 'text-white');
            yearlyBtn.classList.add('text-gray-600', 'hover:text-gray-900');
            monthlyBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            monthlyBtn.classList.add('bg-primary-600', 'text-white');
        }
    }

    async handlePlanSelection(planType) {
        try {
            // Mostrar loading
            this.showLoading(true);

            // Testar conexão com backend apenas quando necessário
            const backendAvailable = await this.testBackendConnection();
            if (!backendAvailable) {
                throw new Error('Servidor temporariamente indisponível. Tente novamente em alguns minutos.');
            }

            // Obter billing cycle
            const billingCycle = this.isYearlyBilling ? 'yearly' : 'monthly';

            // Capturar dados do lead se disponível
            const leadData = this.getLeadData();

            // Criar checkout session
            const response = await fetch(`${STRIPE_CONFIG.apiUrl}/api/stripe/create-checkout-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    planType: planType,
                    billingCycle: billingCycle,
                    leadData: leadData,
                    successUrl: STRIPE_CONFIG.successUrl,
                    cancelUrl: STRIPE_CONFIG.cancelUrl
                })
            });

            if (!response.ok) {
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorMessage;
                } catch (jsonError) {
                    // Se não conseguir fazer parse do JSON, usar mensagem padrão
                    console.warn('Could not parse error response as JSON:', jsonError);
                }

                throw new Error(errorMessage);
            }

            const session = await response.json();

            // Redirecionar para Stripe Checkout
            const { error } = await this.stripe.redirectToCheckout({
                sessionId: session.sessionId
            });

            if (error) {
                throw error;
            }

        } catch (error) {
            console.error('Error handling plan selection:', error);
            this.showError('Erro ao processar pagamento. Tente novamente.');
        } finally {
            this.showLoading(false);
        }
    }

    getLeadData() {
        // Tentar capturar dados de formulários preenchidos
        const leadData = {
            name: '',
            email: '',
            phone: '',
            organization: '',
            source: 'landing_page_checkout',
            utm: this.getUTMParameters()
        };

        // Procurar por campos preenchidos na página
        const nameField = document.querySelector('input[name="name"]');
        const emailField = document.querySelector('input[name="email"]');
        const phoneField = document.querySelector('input[name="phone"]');
        const orgField = document.querySelector('input[name="organization"]');

        if (nameField) leadData.name = nameField.value;
        if (emailField) leadData.email = emailField.value;
        if (phoneField) leadData.phone = phoneField.value;
        if (orgField) leadData.organization = orgField.value;

        return leadData;
    }

    getUTMParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            utm_source: urlParams.get('utm_source'),
            utm_medium: urlParams.get('utm_medium'),
            utm_campaign: urlParams.get('utm_campaign'),
            utm_term: urlParams.get('utm_term'),
            utm_content: urlParams.get('utm_content')
        };
    }

    updatePriceDisplay() {
        // Atualizar exibição de preços baseado no billing cycle
        const prices = this.isYearlyBilling ? {
            basic: Math.round(STRIPE_CONFIG.prices.basic.yearly / 12),
            standard: Math.round(STRIPE_CONFIG.prices.standard.yearly / 12),
            professional: Math.round(STRIPE_CONFIG.prices.professional.yearly / 12)
        } : {
            basic: STRIPE_CONFIG.prices.basic.monthly,
            standard: STRIPE_CONFIG.prices.standard.monthly,
            professional: STRIPE_CONFIG.prices.professional.monthly
        };

        // Atualizar elementos de preço na página
        const basicPrice = document.getElementById('basic-price');
        const standardPrice = document.getElementById('standard-price');
        const professionalPrice = document.getElementById('professional-price');

        if (basicPrice) basicPrice.textContent = `R$ ${prices.basic.toFixed(2).replace('.', ',')}`;
        if (standardPrice) standardPrice.textContent = `R$ ${prices.standard.toFixed(2).replace('.', ',')}`;
        if (professionalPrice) professionalPrice.textContent = `R$ ${prices.professional.toFixed(2).replace('.', ',')}`;

        // Mostrar/esconder indicadores de economia anual
        const basicSavings = document.getElementById('basic-yearly-savings');
        const standardSavings = document.getElementById('standard-yearly-savings');
        const professionalSavings = document.getElementById('professional-yearly-savings');

        if (this.isYearlyBilling) {
            const basicAnnualSavings = (STRIPE_CONFIG.prices.basic.monthly * 12) - STRIPE_CONFIG.prices.basic.yearly;
            const standardAnnualSavings = (STRIPE_CONFIG.prices.standard.monthly * 12) - STRIPE_CONFIG.prices.standard.yearly;
            const professionalAnnualSavings = (STRIPE_CONFIG.prices.professional.monthly * 12) - STRIPE_CONFIG.prices.professional.yearly;

            if (basicSavings) {
                basicSavings.textContent = `Economize R$ ${basicAnnualSavings.toFixed(2).replace('.', ',')}/ano`;
                basicSavings.style.display = 'block';
            }
            if (standardSavings) {
                standardSavings.textContent = `Economize R$ ${standardAnnualSavings.toFixed(2).replace('.', ',')}/ano`;
                standardSavings.style.display = 'block';
            }
            if (professionalSavings) {
                professionalSavings.textContent = `Economize R$ ${professionalAnnualSavings.toFixed(2).replace('.', ',')}/ano`;
                professionalSavings.style.display = 'block';
            }
        } else {
            if (basicSavings) basicSavings.style.display = 'none';
            if (standardSavings) standardSavings.style.display = 'none';
            if (professionalSavings) professionalSavings.style.display = 'none';
        }
    }

    showLoading(show) {
        // Mostrar/esconder indicador de loading
        const loadingElements = document.querySelectorAll('.stripe-loading');
        const buttonElements = document.querySelectorAll('.plan-button[data-plan]');

        if (show) {
            // Mostrar loading
            loadingElements.forEach(el => el.classList.remove('hidden'));
            buttonElements.forEach(el => {
                el.disabled = true;
                // Armazenar texto original
                if (!el.dataset.originalText) {
                    el.dataset.originalText = el.textContent;
                }
                el.innerHTML = `
                    <div class="flex items-center justify-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processando...
                    </div>
                `;
            });
        } else {
            // Esconder loading
            loadingElements.forEach(el => el.classList.add('hidden'));
            buttonElements.forEach(el => {
                el.disabled = false;
                // Restaurar texto original
                if (el.dataset.originalText) {
                    el.textContent = el.dataset.originalText;
                }
            });
        }
    }

    showError(message) {
        // Mostrar mensagem de erro
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50';
        errorDiv.innerHTML = `
            <div class="flex items-center">
                <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(errorDiv);

        // Remover após 5 segundos
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    // Método para criar customer portal (gerenciar assinatura)
    async createCustomerPortal(customerId) {
        try {
            const response = await fetch('/api/stripe/create-customer-portal', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    customerId: customerId,
                    returnUrl: window.location.origin
                })
            });

            if (!response.ok) {
                throw new Error('Failed to create customer portal');
            }

            const { url } = await response.json();
            window.location.href = url;

        } catch (error) {
            console.error('Error creating customer portal:', error);
            this.showError('Erro ao acessar portal do cliente.');
        }
    }

    // Método para verificar status de pagamento
    async checkPaymentStatus(sessionId) {
        try {
            const response = await fetch(`/api/stripe/check-payment-status/${sessionId}`);
            
            if (!response.ok) {
                throw new Error('Failed to check payment status');
            }

            const status = await response.json();
            return status;

        } catch (error) {
            console.error('Error checking payment status:', error);
            return null;
        }
    }
}

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    window.StripeIntegration = new StripeIntegration();
});

// Exportar para uso global
window.StripeIntegration = StripeIntegration;
