/**
 * Serviço para comunicação com a API do backend
 */

class APIService {
    constructor() {
        // Detectar URL da API baseado no ambiente
        this.baseURL = this.getApiBaseUrl();
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutos
    }

    /**
     * Detectar URL base da API baseado no ambiente
     */
    getApiBaseUrl() {
        // Se estiver em produção (Firebase Hosting)
        if (window.location.hostname.includes('firebaseapp.com') ||
            window.location.hostname.includes('web.app') ||
            window.location.hostname.includes('promandato.com')) {
            return 'https://promandato-backend-517140455601.southamerica-east1.run.app/api';
        }

        // Para desenvolvimento local
        return 'http://localhost:3002/api';
    }

    /**
     * Fazer requisição HTTP
     */
    async request(endpoint, options = {}) {
        try {
            const url = `${this.baseURL}${endpoint}`;
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    /**
     * Buscar dados com cache
     */
    async getCachedData(key, fetchFunction, forceRefresh = false) {
        const now = Date.now();
        const cached = this.cache.get(key);

        // Verificar se o cache é válido
        if (!forceRefresh && cached && (now - cached.timestamp) < this.cacheTimeout) {
            console.log(`Cache hit for ${key}`);
            return cached.data;
        }

        try {
            console.log(`Fetching fresh data for ${key}`);
            const data = await fetchFunction();
            
            // Armazenar no cache
            this.cache.set(key, {
                data,
                timestamp: now
            });

            return data;
        } catch (error) {
            // Se falhar e tiver cache, usar cache expirado
            if (cached) {
                console.warn(`Using expired cache for ${key} due to error:`, error);
                return cached.data;
            }
            throw error;
        }
    }

    /**
     * Buscar planos públicos
     */
    async getPublicPlans(forceRefresh = false) {
        return this.getCachedData(
            'public-plans',
            async () => {
                const response = await this.request('/public/plans');
                if (response.success) {
                    return response.data;
                }
                throw new Error(response.error || 'Failed to fetch plans');
            },
            forceRefresh
        );
    }

    /**
     * Enviar formulário de contato
     */
    async submitContactForm(formData) {
        return this.request('/public/contact', {
            method: 'POST',
            body: JSON.stringify(formData)
        });
    }

    /**
     * Solicitar demonstração
     */
    async requestDemo(demoData) {
        return this.request('/public/demo', {
            method: 'POST',
            body: JSON.stringify(demoData)
        });
    }

    /**
     * Limpar cache
     */
    clearCache() {
        this.cache.clear();
        console.log('Cache cleared');
    }

    /**
     * Obter estatísticas do cache
     */
    getCacheStats() {
        const now = Date.now();
        const stats = {
            total: this.cache.size,
            valid: 0,
            expired: 0
        };

        for (const [key, value] of this.cache.entries()) {
            if ((now - value.timestamp) < this.cacheTimeout) {
                stats.valid++;
            } else {
                stats.expired++;
            }
        }

        return stats;
    }
}

/**
 * Gerenciador de planos para a landing page
 */
class PlansManager {
    constructor(apiService) {
        this.api = apiService;
        this.plans = [];
        this.isLoading = false;
        this.error = null;
        this.fallbackPlans = this.getFallbackPlans();
    }

    /**
     * Planos de fallback caso a API não esteja disponível
     */
    getFallbackPlans() {
        return [
            {
                id: 'BASIC',
                name: 'Básico',
                description: 'Ideal para pequenas equipes e projetos iniciais',
                price: { monthly: 169.90, yearly: 1623.00 },
                popular: false,
                badge: null,
                features: {
                    maxUsers: 5,
                    maxDemands: 100,
                    maxCitizens: 500,
                    storageGB: 10,
                    supportLevel: 'basic',
                    aiFeatures: []
                }
            },
            {
                id: 'STANDARD',
                name: 'Padrão',
                description: 'Para equipes em crescimento que precisam de mais recursos',
                price: { monthly: 259.90, yearly: 2491.00 },
                popular: true,
                badge: null,
                features: {
                    maxUsers: 15,
                    maxDemands: 500,
                    maxCitizens: 2000,
                    storageGB: 50,
                    supportLevel: 'priority',
                    aiFeatures: [
                        {
                            id: 'text-analysis',
                            name: 'Análise de Texto',
                            description: 'Análise automática de sentimentos',
                            category: 'Análise',
                            enabled: true
                        }
                    ]
                }
            },
            {
                id: 'PROFESSIONAL',
                name: 'Profissional',
                description: 'Solução completa com IA avançada para grandes mandatos',
                price: { monthly: 449.90, yearly: 4310.00 },
                popular: false,
                badge: 'PREMIUM',
                features: {
                    maxUsers: -1,
                    maxDemands: -1,
                    maxCitizens: -1,
                    storageGB: 200,
                    supportLevel: 'dedicated',
                    aiFeatures: [
                        {
                            id: 'text-analysis',
                            name: 'Análise de Texto',
                            description: 'Análise automática de sentimentos',
                            category: 'Análise',
                            enabled: true
                        },
                        {
                            id: 'auto-categorization',
                            name: 'Categorização Automática',
                            description: 'Categorização inteligente de demandas',
                            category: 'Automação',
                            enabled: true
                        },
                        {
                            id: 'predictive-analysis',
                            name: 'Análise Preditiva',
                            description: 'Previsões baseadas em dados históricos',
                            category: 'Análise',
                            enabled: true
                        },
                        {
                            id: 'smart-responses',
                            name: 'Respostas Inteligentes',
                            description: 'Sugestões automáticas de respostas',
                            category: 'Automação',
                            enabled: true
                        }
                    ]
                }
            }
        ];
    }

    /**
     * Carregar planos da API
     */
    async loadPlans(forceRefresh = false) {
        if (this.isLoading) return this.plans;

        this.isLoading = true;
        this.error = null;

        // Mostrar indicador de carregamento
        this.showLoadingIndicator(true);

        try {
            console.log('Loading plans from API...');
            this.plans = await this.api.getPublicPlans(forceRefresh);
            console.log('Plans loaded successfully:', this.plans);

            // Atualizar interface
            this.updatePricingDisplay();

            return this.plans;
        } catch (error) {
            console.error('Failed to load plans from API:', error);
            this.error = error.message;

            // Usar planos de fallback
            console.log('Using fallback plans');
            this.plans = this.fallbackPlans;
            this.updatePricingDisplay();

            return this.plans;
        } finally {
            this.isLoading = false;
            this.showLoadingIndicator(false);
        }
    }

    /**
     * Mostrar/esconder indicador de carregamento
     */
    showLoadingIndicator(show) {
        const loadingElement = document.getElementById('pricing-loading');
        const cardsElement = document.getElementById('pricing-cards');

        if (loadingElement && cardsElement) {
            if (show) {
                loadingElement.style.display = 'block';
                cardsElement.style.opacity = '0.5';
            } else {
                loadingElement.style.display = 'none';
                cardsElement.style.opacity = '1';
            }
        }
    }

    /**
     * Atualizar exibição de preços na interface
     */
    updatePricingDisplay() {
        if (!this.plans || this.plans.length === 0) return;

        const isYearly = window.state?.isYearlyBilling || false;
        
        this.plans.forEach(plan => {
            const planId = plan.id.toLowerCase();
            
            // Atualizar preço
            const priceElement = document.getElementById(`${planId}-price`);
            if (priceElement) {
                const price = isYearly ? plan.price.yearly : plan.price.monthly;
                priceElement.textContent = this.formatPrice(price);
            }

            // Atualizar informações de economia anual
            const savingsElement = document.getElementById(`${planId}-yearly-savings`);
            if (savingsElement) {
                if (isYearly) {
                    const monthlyTotal = plan.price.monthly * 12;
                    const savings = monthlyTotal - plan.price.yearly;
                    savingsElement.textContent = `Economize ${this.formatPrice(savings)} por ano`;
                    savingsElement.style.display = 'block';
                } else {
                    savingsElement.style.display = 'none';
                }
            }
        });

        console.log('Pricing display updated');
    }

    /**
     * Formatar preço
     */
    formatPrice(price) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(price);
    }

    /**
     * Obter plano por ID
     */
    getPlanById(planId) {
        return this.plans.find(plan => 
            plan.id.toLowerCase() === planId.toLowerCase()
        );
    }

    /**
     * Verificar se há dados atualizados
     */
    async checkForUpdates() {
        try {
            const freshPlans = await this.api.getPublicPlans(true);
            const hasChanges = JSON.stringify(freshPlans) !== JSON.stringify(this.plans);
            
            if (hasChanges) {
                console.log('Plans updated, refreshing display');
                this.plans = freshPlans;
                this.updatePricingDisplay();
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Error checking for updates:', error);
            return false;
        }
    }
}

// Instâncias globais
window.apiService = new APIService();
window.plansManager = new PlansManager(window.apiService);

// Exportar para uso em outros scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIService, PlansManager };
}
