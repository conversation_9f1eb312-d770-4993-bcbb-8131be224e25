{"hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/landingpage", "destination": "/landingpage/index.html"}, {"source": "/landingpage/**", "destination": "/landingpage/index.html"}, {"source": "/", "destination": "/app/index.html"}, {"source": "**", "destination": "/app/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}]}]}, "storage": {"rules": "storage.rules"}, "emulators": {"storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "hosting": {"port": 5000}}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}]}