import React, { createContext, useState, useEffect, ReactNode, useRef, useContext } from 'react';
import { User } from '../types';
import {
  signInUser,
  signOutUser,
  onFirebaseAuthStateChanged,
  signUpUser, // Import the new signUpUser function
  getUserById // Import function to get user data
} from '../services/firebaseService';
import {
  startUserSession,
  endUserSession
} from '../services/onlineUsersService';

interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  error: Error | null;
  signIn: (email: string, pass: string) => Promise<void>;
  signUp: (email: string, pass: string, fullName: string) => Promise<void>; // Add signUp to context type
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>; // Add refresh function
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const previousUserRef = useRef<User | null>(null);

  useEffect(() => {
    console.log("AuthProvider: Inicializando monitoramento de autenticação...");
    setLoading(true);

    const unsubscribe = onFirebaseAuthStateChanged(async (user) => {
      console.log("AuthProvider: Estado de autenticação alterado:", user ? `Usuário ${user.id} autenticado` : "Usuário não autenticado");

      const previousUser = previousUserRef.current;

      try {
        // Se o usuário fez login, iniciar sessão online
        if (user && !previousUser) {
          console.log("🚀 AuthProvider: Iniciando sessão online para:", user.email);
          await startUserSession(user);
          console.log("✅ AuthProvider: Sessão online iniciada para:", user.email);
        }

        // Se o usuário fez logout, finalizar sessão online
        if (!user && previousUser) {
          console.log("🔚 AuthProvider: Finalizando sessão online para:", previousUser.email);
          await endUserSession(previousUser.id);
          console.log("✅ AuthProvider: Sessão online finalizada para:", previousUser.email);
        }
      } catch (error) {
        console.error("❌ AuthProvider: Erro ao gerenciar sessão online:", error);
        // Não bloquear o fluxo de autenticação por erros de sessão online
      }

      previousUserRef.current = user;
      setCurrentUser(user);
      setLoading(false);
    });

    return () => {
      console.log("AuthProvider: Limpando monitoramento de autenticação...");
      unsubscribe();
    }
  }, []); // Array vazio para executar apenas uma vez

  const signIn = async (email: string, pass: string) => {
    console.log("AuthProvider: Iniciando processo de login...");
    setLoading(true);
    setError(null);
    try {
      await signInUser(email, pass);
      console.log("AuthProvider: Login bem-sucedido");
      // onFirebaseAuthStateChanged will handle setting the currentUser
    } catch (err) {
      console.error("AuthProvider: Erro no login:", err);
      let errorMessage = "Falha ao entrar. Verifique suas credenciais.";
      if (err instanceof Error) {
          // You can check for specific Firebase error codes here, e.g., (err as any).code
          if ((err as any).code === 'auth/user-not-found' || (err as any).code === 'auth/wrong-password' || (err as any).code === 'auth/invalid-credential') {
            errorMessage = "Email ou senha inválidos.";
          } else {
            errorMessage = "Ocorreu um erro ao tentar entrar. Tente novamente mais tarde.";
          }
      }
      setError(new Error(errorMessage)); 
      throw new Error(errorMessage); // Re-throw to be caught by UI form if needed
    } finally {
      setLoading(false); // Ensure loading is reset
    }
  };

  const signUp = async (email: string, pass: string, fullName: string) => {
    console.log("AuthProvider: Iniciando processo de cadastro...");
    setLoading(true);
    setError(null);
    try {
      await signUpUser(email, pass, fullName);
      console.log("AuthProvider: Cadastro e login bem-sucedidos");
      // onFirebaseAuthStateChanged will handle setting the currentUser after successful signup & login
    } catch (err) {
      console.error("AuthProvider: Erro no cadastro:", err);
      let errorMessage = "Falha ao cadastrar. Tente novamente.";
      if (err instanceof Error) {
         if ((err as any).code === 'auth/email-already-in-use') {
            errorMessage = "Este email já está em uso. Tente outro.";
        } else if ((err as any).code === 'auth/weak-password') {
            errorMessage = "A senha é muito fraca. Use pelo menos 6 caracteres.";
        } else {
            errorMessage = "Ocorreu um erro ao tentar cadastrar. Tente novamente mais tarde.";
        }
      }
      setError(new Error(errorMessage));
      throw new Error(errorMessage); // Re-throw to be caught by UI form if needed
    } finally {
      setLoading(false); // Ensure loading is reset
    }
  };

  const signOut = async () => {
    console.log("AuthProvider: Iniciando processo de logout...");
    // setLoading(true); // Not strictly necessary here as onFirebaseAuthStateChanged handles it
    setError(null);
    try {
      await signOutUser();
      console.log("AuthProvider: Logout bem-sucedido");
      // onFirebaseAuthStateChanged will handle setting currentUser to null
    } catch (err) {
      console.error("AuthProvider: Erro no logout:", err);
      setError(err as Error);
    } finally {
      // setLoading(false); // onFirebaseAuthStateChanged will set loading to false, so this is fine
    }
  };

  const refreshUser = async () => {
    if (!currentUser) return;
    
    try {
      console.log("AuthProvider: Atualizando dados do usuário...");
      const updatedUser = await getUserById(currentUser.id);
      if (updatedUser) {
        setCurrentUser(updatedUser);
        console.log("AuthProvider: Dados do usuário atualizados:", updatedUser);
      }
    } catch (error) {
      console.error("AuthProvider: Erro ao atualizar dados do usuário:", error);
    }
  };

  return (
    <AuthContext.Provider value={{ currentUser, loading, error, signIn, signUp, signOut, refreshUser }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};